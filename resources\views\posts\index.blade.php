<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Posts') }}
            </h2>
            <a href="{{ route('posts.create') }}"
               class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                Create Post
            </a>
        </div>
    </x-slot>

    <div class="py-8 bg-gray-50 min-h-screen">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="mb-6 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md">
                    {{ session('success') }}
                </div>
            @endif

            <!-- زر إضافي للإنشاء -->
            <div class="mb-6 text-center">
                <a href="{{ route('posts.create') }}"
                   class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors duration-200">
                    Add New Post
                </a>
            </div>

            @if($posts->count() > 0)
                <div class="space-y-6">
                    @foreach ($posts as $post)
                        <div class="bg-white shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <a href="{{ route('posts.show', $post) }}" class="hover:text-blue-600 transition-colors duration-200">
                                        {{ $post->title }}
                                    </a>
                                </h3>
                                <p class="text-gray-600 text-sm leading-relaxed mb-4">{{ Str::limit($post->description, 150) }}</p>

                                <!-- معلومات المؤلف والتاريخ -->
                                <div class="mb-4">
                                    <div class="flex items-center mb-2">
                                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white font-medium text-sm mr-3">
                                            {{ substr($post->user->name ?? 'U', 0, 1) }}
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-800">
                                                Created by {{ $post->user->name ?? 'Unknown Author' }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="ml-11">
                                        <p class="text-xs text-gray-500" title="{{ $post->created_at->format('F j, Y \a\t g:i A') }}">
                                            {{ $post->created_at->diffForHumans() }}
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ route('posts.show', $post) }}"
                                       class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors duration-200">
                                        View
                                    </a>

                                    <a href="{{ route('posts.edit', $post) }}"
                                       class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors duration-200">
                                        Edit
                                    </a>

                                    <form action="{{ route('posts.destroy', $post) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors duration-200"
                                                onclick="return confirm('Are you sure you want to delete this post?')">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-6">
                    {{ $posts->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <div class="bg-white shadow-sm rounded-lg p-8 max-w-md mx-auto border border-gray-200">
                        <div class="text-4xl mb-4 text-gray-400">📝</div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">No posts yet</h3>
                        <p class="text-gray-600 mb-6 text-sm">Start by creating your first post!</p>
                        <a href="{{ route('posts.create') }}"
                           class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                            Create Your First Post
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
