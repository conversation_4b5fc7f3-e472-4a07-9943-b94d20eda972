@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for better readability and professional look */
@layer components {
    .btn-primary {
        @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
    }

    .btn-secondary {
        @apply bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
    }

    .btn-danger {
        @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
    }

    .btn-warning {
        @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
    }

    .card {
        @apply bg-white shadow-sm rounded-lg border border-gray-200;
    }

    .card-hover {
        @apply hover:shadow-md transition-shadow duration-200;
    }

    .text-readable {
        @apply text-gray-800 leading-relaxed;
    }

    .text-muted {
        @apply text-gray-600;
    }

    .text-subtle {
        @apply text-gray-500;
    }
}
